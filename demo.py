#!/usr/bin/env python3
"""
Demo script for the ECU Tuning Visualizer
"""

from tuning_visualizer import <PERSON><PERSON><PERSON>apP<PERSON>, ROMReader, TuningVisualizer
from pathlib import Path

def main():
    # File paths
    kp_file = "8E1910115G__0010.kp"
    bin_file = "IGNITec (VW Golf (LC PnB) - MED9.1.5 - 8E1910115G - 0010).bin"
    
    # Check if files exist
    if not Path(kp_file).exists():
        print(f"Error: {kp_file} not found")
        return
    
    if not Path(bin_file).exists():
        print(f"Error: {bin_file} not found")
        return
    
    print("ECU Tuning Visualizer Demo")
    print("=" * 40)
    
    # Initialize components
    print("\n1. Loading map pack...")
    map_pack = WinOLSMapPack(kp_file)
    
    print("\n2. Loading ROM dump...")
    rom_reader = ROMReader(bin_file)
    
    print("\n3. Creating visualizer...")
    visualizer = TuningVisualizer(map_pack, rom_reader)
    
    # List available maps
    print("\n4. Available maps:")
    maps = map_pack.list_maps()
    for i, map_name in enumerate(maps[:10]):  # Show first 10 maps
        map_info = map_pack.get_map_info(map_name)
        print(f"   {i+1:2d}. {map_name}: {map_info['x_size']}x{map_info['y_size']} at 0x{map_info['address']:X}")
    
    if len(maps) > 10:
        print(f"   ... and {len(maps) - 10} more maps")
    
    # Create output directory
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # Visualize a few interesting maps
    print(f"\n5. Creating visualizations...")
    
    # Find some good maps to visualize
    interesting_maps = []
    for map_name in maps:
        map_info = map_pack.get_map_info(map_name)
        x_size, y_size = map_info['x_size'], map_info['y_size']
        
        # Look for reasonably sized maps (not too small, not too big)
        if 5 <= x_size <= 25 and 5 <= y_size <= 25:
            interesting_maps.append(map_name)
        
        if len(interesting_maps) >= 3:  # Limit to 3 maps for demo
            break
    
    if not interesting_maps:
        # Fallback to first few maps
        interesting_maps = maps[:3]
    
    print(f"   Visualizing: {', '.join(interesting_maps)}")
    
    for map_name in interesting_maps:
        print(f"\n   Processing {map_name}...")
        try:
            # Create 3D surface plot
            visualizer.create_3d_surface_plot(
                map_name, 
                output_dir / f"{map_name}_3d.png"
            )
            
            # Create heatmap
            visualizer.create_heatmap(
                map_name,
                output_dir / f"{map_name}_heatmap.png"
            )
            
            print(f"   ✓ Created visualizations for {map_name}")
            
        except Exception as e:
            print(f"   ✗ Error processing {map_name}: {e}")
    
    print(f"\n6. Demo complete!")
    print(f"   Check the 'output' directory for generated plots.")
    print(f"   Total maps available: {len(maps)}")

if __name__ == '__main__':
    main()
