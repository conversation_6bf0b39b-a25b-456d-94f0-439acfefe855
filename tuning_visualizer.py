#!/usr/bin/env python3
"""
ECU Tuning Visualizer
Reads WinOLS .kp map packs and binary ROM dumps to create 3D surface plots and heatmaps
"""

import struct
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns
import pandas as pd
from pathlib import Path
import argparse
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class WinOLSMapPack:
    """Parser for WinOLS .kp map pack files"""
    
    def __init__(self, kp_file: str):
        self.kp_file = Path(kp_file)
        self.maps = {}
        self.parse_kp_file()
    
    def parse_kp_file(self):
        """Parse the WinOLS .kp file to extract map definitions"""
        print(f"Parsing {self.kp_file}...")
        
        with open(self.kp_file, 'rb') as f:
            data = f.read()
        
        # WinOLS .kp files have a specific binary structure
        # This is a simplified parser - real .kp files are complex
        offset = 0
        
        # Skip header
        if data[:11] == b'\x0b\x00\x00\x00WinOLS File':
            offset = self._find_maps_section(data)
        
        # For now, let's create some common MED9.1.5 maps manually
        # In a full implementation, we'd parse the actual .kp structure
        self._create_common_med9_maps()
    
    def _find_maps_section(self, data: bytes) -> int:
        """Find the maps section in the .kp file"""
        # This would need reverse engineering of the .kp format
        # For now, return a placeholder
        return 0x200
    
    def _create_common_med9_maps(self):
        """Use the KP parser to extract actual maps from the .kp file"""
        try:
            from kp_parser import KPParser
            parser = KPParser(self.kp_file)
            extracted_maps = parser.extract_maps_heuristic()

            # Convert to our format and add some intelligent guessing for common maps
            for map_name, map_info in extracted_maps.items():
                # Try to identify common map types by size and address patterns
                self._enhance_map_info(map_info)
                self.maps[map_name] = map_info

            print(f"Loaded {len(self.maps)} map definitions from .kp file")

        except ImportError:
            print("KP parser not available, using fallback maps")
            self._create_fallback_maps()

    def _enhance_map_info(self, map_info):
        """Enhance map info with intelligent guessing based on size and patterns"""
        x_size = map_info['x_size']
        y_size = map_info['y_size']
        address = map_info['address']

        # Common MED9.1.5 map patterns
        if x_size == 16 and y_size == 12:
            # Likely ignition timing or similar
            map_info['data_type'] = 'uint8'
            map_info['factor'] = 0.75
            map_info['offset'] = -48
            map_info['unit'] = '°KW'
            map_info['x_axis_name'] = 'Engine Load'
            map_info['y_axis_name'] = 'Engine Speed (RPM)'
            if 'timing' in map_info['name'].lower() or 'zw' in map_info['name'].lower():
                map_info['description'] = 'Ignition timing map'
        elif x_size >= 20 and y_size >= 15:
            # Likely fuel or torque map
            map_info['data_type'] = 'uint16'
            map_info['factor'] = 0.1
            map_info['offset'] = 0
            map_info['unit'] = 'Nm or mg/stroke'
            map_info['x_axis_name'] = 'Load/Pedal Position'
            map_info['y_axis_name'] = 'Engine Speed (RPM)'
            map_info['description'] = 'Torque/Fuel map'
        elif x_size <= 8 and y_size <= 8:
            # Likely boost or small calibration map
            map_info['data_type'] = 'uint16'
            map_info['factor'] = 0.01
            map_info['offset'] = 0
            map_info['unit'] = 'bar or factor'
            map_info['x_axis_name'] = 'Load'
            map_info['y_axis_name'] = 'RPM'
            map_info['description'] = 'Boost/calibration map'
        else:
            # Default settings
            map_info['data_type'] = 'uint8'
            map_info['factor'] = 1.0
            map_info['offset'] = 0
            map_info['unit'] = 'raw'
            map_info['x_axis_name'] = 'X Axis'
            map_info['y_axis_name'] = 'Y Axis'

    def _create_fallback_maps(self):
        """Fallback maps if KP parser fails"""
        self.maps = {
            'EXAMPLE_TIMING': {
                'name': 'Example Timing Map',
                'address': 0x1A000,
                'x_size': 16,
                'y_size': 12,
                'data_type': 'uint8',
                'factor': 0.75,
                'offset': -48,
                'unit': '°KW',
                'x_axis_name': 'Engine Load',
                'y_axis_name': 'Engine Speed',
                'description': 'Example ignition timing map'
            }
        }
        print(f"Loaded {len(self.maps)} fallback map definitions")
    
    def get_map_info(self, map_name: str) -> Optional[Dict]:
        """Get information about a specific map"""
        return self.maps.get(map_name)
    
    def list_maps(self) -> List[str]:
        """List all available maps"""
        return list(self.maps.keys())

class ROMReader:
    """Reader for binary ROM dumps"""
    
    def __init__(self, rom_file: str):
        self.rom_file = Path(rom_file)
        with open(self.rom_file, 'rb') as f:
            self.rom_data = f.read()
        print(f"Loaded ROM: {self.rom_file} ({len(self.rom_data)} bytes)")
    
    def read_map_data(self, map_info: Dict) -> np.ndarray:
        """Read map data from ROM based on map definition"""
        address = map_info['address']
        x_size = map_info['x_size']
        y_size = map_info['y_size']
        data_type = map_info['data_type']
        factor = map_info['factor']
        offset = map_info['offset']
        
        # Calculate total bytes needed
        if data_type == 'uint8':
            bytes_per_value = 1
            struct_format = 'B'
        elif data_type == 'uint16':
            bytes_per_value = 2
            struct_format = 'H'
        else:
            raise ValueError(f"Unsupported data type: {data_type}")
        
        total_bytes = x_size * y_size * bytes_per_value
        
        # Check if address is valid
        if address + total_bytes > len(self.rom_data):
            print(f"Warning: Map extends beyond ROM size. Using available data.")
            total_bytes = len(self.rom_data) - address
        
        # Extract raw data
        raw_data = self.rom_data[address:address + total_bytes]
        
        # Unpack binary data
        values = []
        for i in range(0, len(raw_data), bytes_per_value):
            if i + bytes_per_value <= len(raw_data):
                value = struct.unpack('<' + struct_format, raw_data[i:i + bytes_per_value])[0]
                # Apply scaling
                physical_value = value * factor + offset
                values.append(physical_value)
        
        # Reshape to 2D array
        try:
            data_array = np.array(values).reshape(y_size, x_size)
        except ValueError:
            # If we don't have enough data, pad with zeros
            needed_values = x_size * y_size
            while len(values) < needed_values:
                values.append(0)
            data_array = np.array(values[:needed_values]).reshape(y_size, x_size)
        
        return data_array

class TuningVisualizer:
    """Creates visualizations for tuning maps"""
    
    def __init__(self, map_pack: WinOLSMapPack, rom_reader: ROMReader):
        self.map_pack = map_pack
        self.rom_reader = rom_reader
    
    def create_3d_surface_plot(self, map_name: str, save_path: Optional[str] = None):
        """Create a 3D surface plot for a map"""
        map_info = self.map_pack.get_map_info(map_name)
        if not map_info:
            print(f"Map {map_name} not found")
            return
        
        data = self.rom_reader.read_map_data(map_info)
        
        # Create coordinate grids
        x = np.arange(data.shape[1])
        y = np.arange(data.shape[0])
        X, Y = np.meshgrid(x, y)
        
        # Create 3D plot
        fig = plt.figure(figsize=(12, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        surface = ax.plot_surface(X, Y, data, cmap='viridis', alpha=0.8)
        
        ax.set_title(f"{map_info['name']}\n3D Surface Plot", fontsize=14, fontweight='bold')
        ax.set_xlabel(map_info['x_axis_name'])
        ax.set_ylabel(map_info['y_axis_name'])
        ax.set_zlabel(f"Value ({map_info['unit']})")
        
        # Add colorbar
        fig.colorbar(surface, shrink=0.5, aspect=5)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"3D plot saved to {save_path}")
        
        plt.show()
    
    def create_heatmap(self, map_name: str, save_path: Optional[str] = None):
        """Create a heatmap for a map"""
        map_info = self.map_pack.get_map_info(map_name)
        if not map_info:
            print(f"Map {map_name} not found")
            return
        
        data = self.rom_reader.read_map_data(map_info)
        
        # Create heatmap
        plt.figure(figsize=(12, 8))
        
        # Create DataFrame for better labeling
        df = pd.DataFrame(data)
        
        sns.heatmap(df, annot=True, fmt='.1f', cmap='RdYlBu_r', 
                   cbar_kws={'label': f'Value ({map_info["unit"]})'})
        
        plt.title(f"{map_info['name']}\nHeatmap", fontsize=14, fontweight='bold')
        plt.xlabel(map_info['x_axis_name'])
        plt.ylabel(map_info['y_axis_name'])
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Heatmap saved to {save_path}")
        
        plt.show()
    
    def create_comparison_plot(self, map_names: List[str], save_path: Optional[str] = None):
        """Create a comparison plot for multiple maps"""
        fig, axes = plt.subplots(2, len(map_names), figsize=(6*len(map_names), 10))
        if len(map_names) == 1:
            axes = axes.reshape(-1, 1)
        
        for i, map_name in enumerate(map_names):
            map_info = self.map_pack.get_map_info(map_name)
            if not map_info:
                continue
            
            data = self.rom_reader.read_map_data(map_info)
            
            # 3D surface (top row)
            ax_3d = fig.add_subplot(2, len(map_names), i+1, projection='3d')
            x = np.arange(data.shape[1])
            y = np.arange(data.shape[0])
            X, Y = np.meshgrid(x, y)
            ax_3d.plot_surface(X, Y, data, cmap='viridis', alpha=0.8)
            ax_3d.set_title(f"{map_info['name']}\n3D Surface")
            
            # Heatmap (bottom row)
            ax_heat = axes[1, i]
            sns.heatmap(data, ax=ax_heat, cmap='RdYlBu_r', 
                       cbar_kws={'label': f'({map_info["unit"]})'})
            ax_heat.set_title(f"{map_info['name']}\nHeatmap")
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Comparison plot saved to {save_path}")
        
        plt.show()

def main():
    parser = argparse.ArgumentParser(description='ECU Tuning Visualizer')
    parser.add_argument('kp_file', help='Path to WinOLS .kp map pack file')
    parser.add_argument('bin_file', help='Path to binary ROM dump')
    parser.add_argument('--map', help='Specific map to visualize')
    parser.add_argument('--list-maps', action='store_true', help='List available maps')
    parser.add_argument('--output-dir', default='output', help='Output directory for plots')
    
    args = parser.parse_args()
    
    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # Initialize components
    map_pack = WinOLSMapPack(args.kp_file)
    rom_reader = ROMReader(args.bin_file)
    visualizer = TuningVisualizer(map_pack, rom_reader)
    
    if args.list_maps:
        print("\nAvailable maps:")
        for map_name in map_pack.list_maps():
            map_info = map_pack.get_map_info(map_name)
            print(f"  {map_name}: {map_info['description']}")
        return
    
    if args.map:
        # Visualize specific map
        print(f"\nCreating visualizations for {args.map}...")
        visualizer.create_3d_surface_plot(args.map, output_dir / f"{args.map}_3d.png")
        visualizer.create_heatmap(args.map, output_dir / f"{args.map}_heatmap.png")
    else:
        # Visualize all maps
        print("\nCreating visualizations for all maps...")
        for map_name in map_pack.list_maps():
            print(f"Processing {map_name}...")
            visualizer.create_3d_surface_plot(map_name, output_dir / f"{map_name}_3d.png")
            visualizer.create_heatmap(map_name, output_dir / f"{map_name}_heatmap.png")

if __name__ == '__main__':
    main()
