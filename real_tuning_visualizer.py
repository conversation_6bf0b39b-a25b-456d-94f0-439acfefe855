#!/usr/bin/env python3
"""
Real ECU Tuning Visualizer
Uses actual MED9.1.5 map definitions to create accurate visualizations
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import seaborn as sns
import pandas as pd
from pathlib import Path
import struct
from med9_maps import MED9_MAPS, find_maps_in_rom

class RealROMReader:
    """Reader for binary ROM dumps with actual map validation"""
    
    def __init__(self, rom_file: str):
        self.rom_file = Path(rom_file)
        with open(self.rom_file, 'rb') as f:
            self.rom_data = f.read()
        print(f"Loaded ROM: {self.rom_file} ({len(self.rom_data)} bytes)")
        
        # Find actual maps in the ROM
        self.maps = find_maps_in_rom(self.rom_data)
        print(f"Validated {len(self.maps)} maps in ROM")
    
    def read_map_data(self, map_name: str) -> np.ndarray:
        """Read map data from ROM"""
        if map_name not in self.maps:
            raise ValueError(f"Map {map_name} not found")
        
        map_info = self.maps[map_name]
        address = map_info['address']
        x_size = map_info['x_size']
        y_size = map_info['y_size']
        data_type = map_info['data_type']
        factor = map_info['factor']
        offset = map_info['offset']
        
        # Calculate bytes needed
        bytes_per_value = 2 if data_type == 'uint16' else 1
        total_bytes = x_size * y_size * bytes_per_value
        
        # Extract raw data
        raw_data = self.rom_data[address:address + total_bytes]
        
        # Convert to values
        values = []
        for i in range(0, len(raw_data), bytes_per_value):
            if bytes_per_value == 2:
                value = struct.unpack('<H', raw_data[i:i+2])[0]
            else:
                value = raw_data[i]
            
            # Apply scaling
            physical_value = value * factor + offset
            values.append(physical_value)
        
        # Reshape to 2D array (row-major order)
        data_array = np.array(values).reshape(y_size, x_size)
        return data_array
    
    def get_map_info(self, map_name: str):
        """Get map information"""
        return self.maps.get(map_name)
    
    def list_maps(self):
        """List available maps"""
        return list(self.maps.keys())

class RealTuningVisualizer:
    """Creates accurate visualizations for real tuning maps"""
    
    def __init__(self, rom_reader: RealROMReader):
        self.rom_reader = rom_reader
    
    def create_3d_surface_plot(self, map_name: str, save_path: str = None):
        """Create a 3D surface plot for a map"""
        map_info = self.rom_reader.get_map_info(map_name)
        if not map_info:
            print(f"Map {map_name} not found")
            return
        
        data = self.rom_reader.read_map_data(map_name)
        
        # Create coordinate grids
        x = np.arange(data.shape[1])
        y = np.arange(data.shape[0])
        X, Y = np.meshgrid(x, y)
        
        # Create 3D plot
        fig = plt.figure(figsize=(14, 10))
        ax = fig.add_subplot(111, projection='3d')
        
        # Choose colormap based on map type
        if 'timing' in map_info['name'].lower() or '°KW' in map_info['unit']:
            cmap = 'RdYlBu_r'  # Red for advanced timing, blue for retarded
        elif 'boost' in map_info['name'].lower() or 'bar' in map_info['unit']:
            cmap = 'plasma'  # Good for pressure
        elif 'torque' in map_info['name'].lower() or 'Nm' in map_info['unit']:
            cmap = 'viridis'  # Good for torque
        else:
            cmap = 'coolwarm'
        
        surface = ax.plot_surface(X, Y, data, cmap=cmap, alpha=0.9, 
                                linewidth=0.5, edgecolors='black', linewidths=0.1)
        
        ax.set_title(f"{map_info['name']}\n3D Surface Plot", fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel(f"{map_info['x_axis_name']}", fontsize=12)
        ax.set_ylabel(f"{map_info['y_axis_name']}", fontsize=12)
        ax.set_zlabel(f"Value ({map_info['unit']})", fontsize=12)
        
        # Add statistics
        stats_text = f"Min: {np.min(data):.2f} {map_info['unit']}\n"
        stats_text += f"Max: {np.max(data):.2f} {map_info['unit']}\n"
        stats_text += f"Mean: {np.mean(data):.2f} {map_info['unit']}"
        
        ax.text2D(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
                 verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # Add colorbar
        cbar = fig.colorbar(surface, shrink=0.6, aspect=20, pad=0.1)
        cbar.set_label(f'Value ({map_info["unit"]})', fontsize=12)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"3D plot saved to {save_path}")
        
        plt.show()
    
    def create_heatmap(self, map_name: str, save_path: str = None):
        """Create a heatmap for a map"""
        map_info = self.rom_reader.get_map_info(map_name)
        if not map_info:
            print(f"Map {map_name} not found")
            return
        
        data = self.rom_reader.read_map_data(map_name)
        
        # Create heatmap
        plt.figure(figsize=(14, 10))
        
        # Choose colormap
        if 'timing' in map_info['name'].lower() or '°KW' in map_info['unit']:
            cmap = 'RdYlBu_r'
            center = None  # Let it auto-center
        elif 'boost' in map_info['name'].lower() or 'bar' in map_info['unit']:
            cmap = 'plasma'
            center = None
        elif 'lambda' in map_info['name'].lower():
            cmap = 'RdYlGn'
            center = 1.0  # Stoichiometric lambda
        else:
            cmap = 'viridis'
            center = None
        
        # Create the heatmap
        sns.heatmap(data, annot=True, fmt='.1f', cmap=cmap, center=center,
                   cbar_kws={'label': f'Value ({map_info["unit"]})'}, 
                   linewidths=0.5, linecolor='white')
        
        plt.title(f"{map_info['name']}\nHeatmap", fontsize=16, fontweight='bold', pad=20)
        plt.xlabel(f"{map_info['x_axis_name']}", fontsize=12)
        plt.ylabel(f"{map_info['y_axis_name']}", fontsize=12)
        
        # Add statistics
        stats_text = f"Min: {np.min(data):.2f}, Max: {np.max(data):.2f}, Mean: {np.mean(data):.2f} {map_info['unit']}"
        plt.figtext(0.5, 0.02, stats_text, ha='center', fontsize=10, 
                   bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Heatmap saved to {save_path}")
        
        plt.show()
    
    def create_comparison_plot(self, map_names: list, save_path: str = None):
        """Create a comparison plot for multiple maps"""
        n_maps = len(map_names)
        fig, axes = plt.subplots(2, n_maps, figsize=(6*n_maps, 12))
        
        if n_maps == 1:
            axes = axes.reshape(-1, 1)
        
        for i, map_name in enumerate(map_names):
            map_info = self.rom_reader.get_map_info(map_name)
            if not map_info:
                continue
            
            data = self.rom_reader.read_map_data(map_name)
            
            # 3D surface (top row)
            ax_3d = fig.add_subplot(2, n_maps, i+1, projection='3d')
            x = np.arange(data.shape[1])
            y = np.arange(data.shape[0])
            X, Y = np.meshgrid(x, y)
            
            if 'timing' in map_info['name'].lower():
                cmap = 'RdYlBu_r'
            elif 'boost' in map_info['name'].lower():
                cmap = 'plasma'
            else:
                cmap = 'viridis'
            
            ax_3d.plot_surface(X, Y, data, cmap=cmap, alpha=0.8)
            ax_3d.set_title(f"{map_name}\n3D Surface", fontsize=12)
            ax_3d.set_xlabel(map_info['x_axis_name'], fontsize=10)
            ax_3d.set_ylabel(map_info['y_axis_name'], fontsize=10)
            ax_3d.set_zlabel(f"({map_info['unit']})", fontsize=10)
            
            # Heatmap (bottom row)
            ax_heat = axes[1, i]
            sns.heatmap(data, ax=ax_heat, cmap=cmap, 
                       cbar_kws={'label': f'({map_info["unit"]})'})
            ax_heat.set_title(f"{map_name}\nHeatmap", fontsize=12)
            ax_heat.set_xlabel(map_info['x_axis_name'], fontsize=10)
            ax_heat.set_ylabel(map_info['y_axis_name'], fontsize=10)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Comparison plot saved to {save_path}")
        
        plt.show()
    
    def create_map_summary(self, save_path: str = None):
        """Create a summary of all maps"""
        maps = self.rom_reader.list_maps()
        
        print("\n=== ECU Map Summary ===")
        print(f"ROM File: {self.rom_reader.rom_file}")
        print(f"Total Maps: {len(maps)}")
        print("-" * 80)
        
        summary_data = []
        for map_name in maps:
            map_info = self.rom_reader.get_map_info(map_name)
            data = self.rom_reader.read_map_data(map_name)
            
            summary_data.append({
                'Map': map_name,
                'Address': f"0x{map_info['address']:X}",
                'Size': f"{map_info['x_size']}x{map_info['y_size']}",
                'Unit': map_info['unit'],
                'Min': f"{np.min(data):.2f}",
                'Max': f"{np.max(data):.2f}",
                'Mean': f"{np.mean(data):.2f}",
                'Description': map_info['description']
            })
        
        df = pd.DataFrame(summary_data)
        print(df.to_string(index=False))
        
        if save_path:
            df.to_csv(save_path, index=False)
            print(f"\nSummary saved to {save_path}")

def main():
    """Demo of the real tuning visualizer"""
    rom_file = "IGNITec (VW Golf (LC PnB) - MED9.1.5 - 8E1910115G - 0010).bin"
    
    if not Path(rom_file).exists():
        print(f"Error: {rom_file} not found")
        return
    
    print("Real ECU Tuning Visualizer")
    print("=" * 50)
    
    # Initialize
    rom_reader = RealROMReader(rom_file)
    visualizer = RealTuningVisualizer(rom_reader)
    
    # Create output directory
    output_dir = Path("real_output")
    output_dir.mkdir(exist_ok=True)
    
    # Create summary
    visualizer.create_map_summary(output_dir / "map_summary.csv")
    
    # Visualize key maps
    key_maps = ['KFZW', 'KFMIRL', 'LDRXN', 'LAMFA']  # Timing, Torque, Boost, Lambda
    
    print(f"\nCreating visualizations for key maps: {', '.join(key_maps)}")
    
    for map_name in key_maps:
        if map_name in rom_reader.list_maps():
            print(f"\nProcessing {map_name}...")
            visualizer.create_3d_surface_plot(map_name, output_dir / f"{map_name}_3d.png")
            visualizer.create_heatmap(map_name, output_dir / f"{map_name}_heatmap.png")
    
    # Create comparison plot
    print("\nCreating comparison plot...")
    visualizer.create_comparison_plot(key_maps, output_dir / "comparison.png")
    
    print(f"\nVisualization complete! Check the '{output_dir}' directory for all plots.")

if __name__ == '__main__':
    main()
