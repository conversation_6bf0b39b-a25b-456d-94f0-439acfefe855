#!/usr/bin/env python3
"""
ECU Axis Reader
Reads RPM and load axis data for MED9.1.5 maps
"""

import struct
import numpy as np
from typing import Tu<PERSON>, List

class AxisReader:
    """Reads axis data (RPM, load, etc.) for ECU maps"""
    
    def __init__(self, rom_data: bytes):
        self.rom_data = rom_data
        
        # Common MED9.1.5 axis locations (these may need adjustment)
        self.axis_definitions = {
            'rpm_16': {
                'address': 0x18000,  # Common RPM axis location
                'size': 16,
                'data_type': 'uint16',
                'factor': 0.25,
                'offset': 0,
                'unit': 'RPM'
            },
            'rpm_12': {
                'address': 0x18020,
                'size': 12,
                'data_type': 'uint16', 
                'factor': 0.25,
                'offset': 0,
                'unit': 'RPM'
            },
            'rpm_8': {
                'address': 0x18040,
                'size': 8,
                'data_type': 'uint16',
                'factor': 0.25,
                'offset': 0,
                'unit': 'RPM'
            },
            'load_16': {
                'address': 0x18100,
                'size': 16,
                'data_type': 'uint8',
                'factor': 1.0,
                'offset': 0,
                'unit': '%'
            },
            'load_12': {
                'address': 0x18120,
                'size': 12,
                'data_type': 'uint8',
                'factor': 1.0,
                'offset': 0,
                'unit': '%'
            },
            'pedal_16': {
                'address': 0x18200,
                'size': 16,
                'data_type': 'uint8',
                'factor': 1.0,
                'offset': 0,
                'unit': '%'
            }
        }
    
    def read_axis(self, axis_name: str) -> np.ndarray:
        """Read axis data"""
        if axis_name not in self.axis_definitions:
            # Return default axis if not found
            axis_def = self.axis_definitions.get(axis_name.replace('_', '_16'), None)
            if not axis_def:
                print(f"Warning: Unknown axis {axis_name}, using default")
                return self.create_default_axis(16)
        else:
            axis_def = self.axis_definitions[axis_name]
        
        address = axis_def['address']
        size = axis_def['size']
        data_type = axis_def['data_type']
        factor = axis_def['factor']
        offset = axis_def['offset']
        
        # Check if address is valid
        bytes_per_value = 2 if data_type == 'uint16' else 1
        total_bytes = size * bytes_per_value
        
        if address + total_bytes > len(self.rom_data):
            print(f"Warning: Axis {axis_name} extends beyond ROM, using default")
            return self.create_default_axis(size, axis_def['unit'])
        
        # Read raw data
        values = []
        for i in range(size):
            byte_offset = address + (i * bytes_per_value)
            if data_type == 'uint16':
                raw_value = struct.unpack('<H', self.rom_data[byte_offset:byte_offset+2])[0]
            else:
                raw_value = self.rom_data[byte_offset]
            
            # Apply scaling
            physical_value = raw_value * factor + offset
            values.append(physical_value)
        
        return np.array(values)
    
    def create_default_axis(self, size: int, unit: str = '') -> np.ndarray:
        """Create a default axis when real data isn't available"""
        if 'RPM' in unit:
            # Create reasonable RPM range
            return np.linspace(800, 6400, size)
        elif '%' in unit and 'pedal' in unit.lower():
            # Pedal position
            return np.linspace(0, 100, size)
        elif '%' in unit:
            # Load percentage
            return np.linspace(10, 100, size)
        else:
            # Generic
            return np.arange(size)
    
    def find_axis_data(self, expected_size: int, axis_type: str = 'rpm') -> np.ndarray:
        """Try to find axis data by searching for reasonable patterns"""
        print(f"Searching for {axis_type} axis with {expected_size} points...")
        
        # Search parameters based on axis type
        if axis_type == 'rpm':
            search_ranges = [(0x17000, 0x19000), (0x1F000, 0x21000)]
            min_val, max_val = 500, 8000  # Reasonable RPM range
            data_type = 'uint16'
            factor = 0.25  # Common RPM scaling
        elif axis_type == 'load':
            search_ranges = [(0x18000, 0x19000), (0x1F000, 0x20000)]
            min_val, max_val = 0, 200  # Load percentage
            data_type = 'uint8'
            factor = 1.0
        else:
            return self.create_default_axis(expected_size)
        
        bytes_per_value = 2 if data_type == 'uint16' else 1
        total_bytes = expected_size * bytes_per_value
        
        best_candidate = None
        best_score = 0
        
        for start_addr, end_addr in search_ranges:
            for addr in range(start_addr, min(end_addr, len(self.rom_data) - total_bytes), 2):
                try:
                    # Read potential axis data
                    values = []
                    for i in range(expected_size):
                        byte_offset = addr + (i * bytes_per_value)
                        if data_type == 'uint16':
                            raw_value = struct.unpack('<H', self.rom_data[byte_offset:byte_offset+2])[0]
                        else:
                            raw_value = self.rom_data[byte_offset]
                        
                        physical_value = raw_value * factor
                        values.append(physical_value)
                    
                    # Score this candidate
                    score = self.score_axis_candidate(values, min_val, max_val, axis_type)
                    
                    if score > best_score:
                        best_score = score
                        best_candidate = (addr, values)
                        
                except:
                    continue
        
        if best_candidate and best_score > 5:  # Minimum score threshold
            addr, values = best_candidate
            print(f"  Found {axis_type} axis at 0x{addr:X}: {values[0]:.0f} to {values[-1]:.0f}")
            return np.array(values)
        else:
            print(f"  No suitable {axis_type} axis found, using default")
            return self.create_default_axis(expected_size, 'RPM' if axis_type == 'rpm' else '%')
    
    def score_axis_candidate(self, values: List[float], min_val: float, max_val: float, axis_type: str) -> int:
        """Score how likely this is to be a real axis"""
        if len(values) < 2:
            return 0
        
        score = 0
        
        # Check if values are in reasonable range
        if min_val <= min(values) and max(values) <= max_val:
            score += 3
        
        # Check if values are monotonically increasing (common for axes)
        if all(values[i] <= values[i+1] for i in range(len(values)-1)):
            score += 3
        
        # Check for reasonable spacing
        diffs = [values[i+1] - values[i] for i in range(len(values)-1)]
        if len(set(diffs)) > 1:  # Not all the same (more realistic)
            score += 2
        
        # Check for reasonable step sizes
        avg_step = sum(diffs) / len(diffs)
        if axis_type == 'rpm' and 50 <= avg_step <= 800:
            score += 2
        elif axis_type == 'load' and 2 <= avg_step <= 20:
            score += 2
        
        return score

def test_axis_reader():
    """Test the axis reader"""
    rom_file = "IGNITec (VW Golf (LC PnB) - MED9.1.5 - 8E1910115G - 0010).bin"
    
    with open(rom_file, 'rb') as f:
        rom_data = f.read()
    
    reader = AxisReader(rom_data)
    
    print("Testing axis reader...")
    
    # Test finding RPM axes
    rpm_16 = reader.find_axis_data(16, 'rpm')
    rpm_12 = reader.find_axis_data(12, 'rpm')
    rpm_8 = reader.find_axis_data(8, 'rpm')
    
    print(f"\nRPM 16: {rpm_16}")
    print(f"RPM 12: {rpm_12}")
    print(f"RPM 8: {rpm_8}")
    
    # Test finding load axes
    load_16 = reader.find_axis_data(16, 'load')
    load_12 = reader.find_axis_data(12, 'load')
    
    print(f"\nLoad 16: {load_16}")
    print(f"Load 12: {load_12}")

if __name__ == '__main__':
    test_axis_reader()
