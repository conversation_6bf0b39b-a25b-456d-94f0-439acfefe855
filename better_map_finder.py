#!/usr/bin/env python3
"""
Better Map Finder - More systematic approach to finding correct map addresses
"""

import struct
import numpy as np
from pathlib import Path
from typing import List, Tu<PERSON>, Dict

class MapFinder:
    """Systematic map finder for MED9.1.5"""
    
    def __init__(self, rom_file: str):
        self.rom_file = Path(rom_file)
        with open(self.rom_file, 'rb') as f:
            self.rom_data = f.read()
        print(f"Loaded ROM: {len(self.rom_data)} bytes")
    
    def find_timing_maps(self) -> List[Dict]:
        """Find ignition timing maps by looking for characteristic patterns"""
        print("Searching for ignition timing maps...")
        
        candidates = []
        
        # Common MED9.1.5 calibration areas
        search_ranges = [
            (0x10000, 0x30000),  # Main cal area
            (0x80000, 0xA0000),  # Secondary area
        ]
        
        for start_addr, end_addr in search_ranges:
            print(f"  Searching 0x{start_addr:X} - 0x{end_addr:X}")
            
            # Search for 16x12 uint8 maps (common timing map size)
            for addr in range(start_addr, min(end_addr, len(self.rom_data) - 192), 0x10):
                candidate = self.analyze_timing_candidate(addr, 16, 12)
                if candidate:
                    candidates.append(candidate)
        
        # Sort by score
        candidates.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"Found {len(candidates)} timing map candidates")
        return candidates[:10]  # Return top 10
    
    def analyze_timing_candidate(self, address: int, x_size: int, y_size: int) -> Dict:
        """Analyze if an address contains a timing map"""
        total_bytes = x_size * y_size
        
        if address + total_bytes > len(self.rom_data):
            return None
        
        # Read raw data
        raw_data = self.rom_data[address:address + total_bytes]
        values = list(raw_data)
        
        score = 0
        
        # Test different scaling factors
        scaling_tests = [
            (0.75, -48),   # Common MED9 timing scaling
            (0.5, -32),    # Alternative scaling
            (1.0, -64),    # Another possibility
            (0.375, -24),  # Half resolution
        ]
        
        best_scaling = None
        best_score = 0
        
        for factor, offset in scaling_tests:
            scaled_values = [v * factor + offset for v in values]
            test_score = self.score_timing_values(scaled_values)
            
            if test_score > best_score:
                best_score = test_score
                best_scaling = (factor, offset)
        
        if best_score < 3:  # Minimum threshold
            return None
        
        factor, offset = best_scaling
        scaled_values = [v * factor + offset for v in values]
        data_array = np.array(scaled_values).reshape(y_size, x_size)
        
        return {
            'address': address,
            'x_size': x_size,
            'y_size': y_size,
            'factor': factor,
            'offset': offset,
            'score': best_score,
            'min_value': np.min(data_array),
            'max_value': np.max(data_array),
            'mean_value': np.mean(data_array),
            'data': data_array
        }
    
    def score_timing_values(self, values: List[float]) -> int:
        """Score how likely these values are to be ignition timing"""
        if not values:
            return 0
        
        score = 0
        min_val, max_val = min(values), max(values)
        mean_val = sum(values) / len(values)
        
        # Reasonable timing range (-20 to +40 degrees)
        if -25 <= min_val <= 5 and 10 <= max_val <= 45:
            score += 5
        elif -30 <= min_val <= 10 and 5 <= max_val <= 50:
            score += 3
        
        # Reasonable mean (typically 5-25 degrees)
        if 0 <= mean_val <= 30:
            score += 2
        
        # Good spread of values
        if (max_val - min_val) > 15:
            score += 2
        
        # Not too many extreme values
        extreme_count = sum(1 for v in values if v < -20 or v > 40)
        if extreme_count < len(values) * 0.1:  # Less than 10% extreme
            score += 1
        
        return score
    
    def find_boost_maps(self) -> List[Dict]:
        """Find boost pressure maps"""
        print("Searching for boost pressure maps...")
        
        candidates = []
        
        # Boost maps are typically smaller (8x12 or 12x8) and uint16
        search_ranges = [(0x10000, 0x30000), (0x80000, 0xA0000)]
        
        for start_addr, end_addr in search_ranges:
            for addr in range(start_addr, min(end_addr, len(self.rom_data) - 192), 0x10):
                # Try 12x8 uint16
                candidate = self.analyze_boost_candidate(addr, 12, 8)
                if candidate:
                    candidates.append(candidate)
        
        candidates.sort(key=lambda x: x['score'], reverse=True)
        return candidates[:5]
    
    def analyze_boost_candidate(self, address: int, x_size: int, y_size: int) -> Dict:
        """Analyze if an address contains a boost map"""
        total_bytes = x_size * y_size * 2  # uint16
        
        if address + total_bytes > len(self.rom_data):
            return None
        
        # Read raw data as uint16
        values = []
        for i in range(0, total_bytes, 2):
            val = struct.unpack('<H', self.rom_data[address + i:address + i + 2])[0]
            values.append(val)
        
        # Test scaling factors for boost (typically 0.01 or 0.001)
        scaling_tests = [(0.01, 0), (0.001, 0), (0.1, 0)]
        
        best_scaling = None
        best_score = 0
        
        for factor, offset in scaling_tests:
            scaled_values = [v * factor + offset for v in values]
            test_score = self.score_boost_values(scaled_values)
            
            if test_score > best_score:
                best_score = test_score
                best_scaling = (factor, offset)
        
        if best_score < 3:
            return None
        
        factor, offset = best_scaling
        scaled_values = [v * factor + offset for v in values]
        data_array = np.array(scaled_values).reshape(y_size, x_size)
        
        return {
            'address': address,
            'x_size': x_size,
            'y_size': y_size,
            'factor': factor,
            'offset': offset,
            'score': best_score,
            'min_value': np.min(data_array),
            'max_value': np.max(data_array),
            'mean_value': np.mean(data_array),
            'data': data_array
        }
    
    def score_boost_values(self, values: List[float]) -> int:
        """Score how likely these values are to be boost pressure"""
        if not values:
            return 0
        
        score = 0
        min_val, max_val = min(values), max(values)
        mean_val = sum(values) / len(values)
        
        # Reasonable boost range (0.5 to 3.0 bar absolute)
        if 0.5 <= min_val <= 1.2 and 1.5 <= max_val <= 3.5:
            score += 5
        elif 0.0 <= min_val <= 1.5 and 1.0 <= max_val <= 4.0:
            score += 3
        
        # Reasonable mean
        if 1.0 <= mean_val <= 2.5:
            score += 2
        
        # Good spread
        if (max_val - min_val) > 0.5:
            score += 2
        
        return score
    
    def create_report(self, output_file: str = "map_analysis_report.txt"):
        """Create a comprehensive report"""
        with open(output_file, 'w') as f:
            f.write(f"ECU Map Analysis Report\n")
            f.write(f"ROM File: {self.rom_file}\n")
            f.write(f"ROM Size: {len(self.rom_data)} bytes\n")
            f.write("=" * 60 + "\n\n")
            
            # Find timing maps
            timing_maps = self.find_timing_maps()
            f.write("IGNITION TIMING MAPS:\n")
            f.write("-" * 30 + "\n")
            
            for i, map_info in enumerate(timing_maps):
                f.write(f"{i+1}. Address: 0x{map_info['address']:X}\n")
                f.write(f"   Size: {map_info['x_size']}x{map_info['y_size']}\n")
                f.write(f"   Scaling: {map_info['factor']} * value + {map_info['offset']}\n")
                f.write(f"   Range: {map_info['min_value']:.1f} to {map_info['max_value']:.1f} °KW\n")
                f.write(f"   Mean: {map_info['mean_value']:.1f} °KW\n")
                f.write(f"   Score: {map_info['score']}\n\n")
            
            # Find boost maps
            boost_maps = self.find_boost_maps()
            f.write("BOOST PRESSURE MAPS:\n")
            f.write("-" * 30 + "\n")
            
            for i, map_info in enumerate(boost_maps):
                f.write(f"{i+1}. Address: 0x{map_info['address']:X}\n")
                f.write(f"   Size: {map_info['x_size']}x{map_info['y_size']}\n")
                f.write(f"   Scaling: {map_info['factor']} * value + {map_info['offset']}\n")
                f.write(f"   Range: {map_info['min_value']:.2f} to {map_info['max_value']:.2f} bar\n")
                f.write(f"   Mean: {map_info['mean_value']:.2f} bar\n")
                f.write(f"   Score: {map_info['score']}\n\n")
        
        print(f"Analysis report saved to {output_file}")

def main():
    rom_file = "IGNITec (VW Golf (LC PnB) - MED9.1.5 - 8E1910115G - 0010).bin"
    
    if not Path(rom_file).exists():
        print(f"Error: {rom_file} not found")
        return
    
    finder = MapFinder(rom_file)
    
    # Find timing maps
    timing_maps = finder.find_timing_maps()
    
    print(f"\nTop timing map candidates:")
    for i, map_info in enumerate(timing_maps[:5]):
        print(f"{i+1}. 0x{map_info['address']:X}: "
              f"{map_info['min_value']:.1f} to {map_info['max_value']:.1f} °KW "
              f"(score: {map_info['score']})")
    
    # Find boost maps
    boost_maps = finder.find_boost_maps()
    
    print(f"\nTop boost map candidates:")
    for i, map_info in enumerate(boost_maps[:3]):
        print(f"{i+1}. 0x{map_info['address']:X}: "
              f"{map_info['min_value']:.2f} to {map_info['max_value']:.2f} bar "
              f"(score: {map_info['score']})")
    
    # Create detailed report
    finder.create_report()

if __name__ == '__main__':
    main()
