#!/usr/bin/env python3
"""
Map Inspector - Manually inspect map data to verify correctness
"""

import struct
import numpy as np
from pathlib import Path

def inspect_map_data(rom_file: str, address: int, x_size: int, y_size: int, data_type: str = 'uint8'):
    """Inspect map data at a specific address"""
    with open(rom_file, 'rb') as f:
        rom_data = f.read()
    
    bytes_per_value = 2 if data_type == 'uint16' else 1
    total_bytes = x_size * y_size * bytes_per_value
    
    print(f"Inspecting map at 0x{address:X}")
    print(f"Size: {x_size}x{y_size}, Type: {data_type}")
    print(f"Total bytes: {total_bytes}")
    
    if address + total_bytes > len(rom_data):
        print("ERROR: Address extends beyond ROM size")
        return
    
    # Read raw data
    raw_data = rom_data[address:address + total_bytes]
    
    # Show hex dump of first few bytes
    print(f"\nHex dump (first 32 bytes):")
    for i in range(0, min(32, len(raw_data)), 16):
        hex_part = ' '.join(f'{b:02x}' for b in raw_data[i:i+16])
        print(f"  {address+i:06X}: {hex_part}")
    
    # Convert to values
    values = []
    for i in range(0, len(raw_data), bytes_per_value):
        if bytes_per_value == 2:
            value = struct.unpack('<H', raw_data[i:i+2])[0]
        else:
            value = raw_data[i]
        values.append(value)
    
    # Reshape and display
    data_array = np.array(values).reshape(y_size, x_size)
    
    print(f"\nRaw values (first 5 rows):")
    for i in range(min(5, y_size)):
        row_str = ' '.join(f'{val:4d}' for val in data_array[i, :min(8, x_size)])
        if x_size > 8:
            row_str += " ..."
        print(f"  Row {i}: {row_str}")
    
    print(f"\nStatistics:")
    print(f"  Min: {np.min(data_array)}")
    print(f"  Max: {np.max(data_array)}")
    print(f"  Mean: {np.mean(data_array):.1f}")
    print(f"  Unique values: {len(np.unique(data_array))}")
    
    return data_array

def inspect_timing_map_with_scaling(rom_file: str, address: int = 0x1A000):
    """Inspect timing map with proper scaling"""
    print("=== TIMING MAP INSPECTION ===")
    
    raw_data = inspect_map_data(rom_file, address, 16, 12, 'uint8')
    
    if raw_data is not None:
        # Apply timing scaling: value * 0.75 - 48
        scaled_data = raw_data * 0.75 - 48
        
        print(f"\nScaled values (degrees KW, first 5 rows):")
        for i in range(min(5, scaled_data.shape[0])):
            row_str = ' '.join(f'{val:5.1f}' for val in scaled_data[i, :min(8, scaled_data.shape[1])])
            if scaled_data.shape[1] > 8:
                row_str += " ..."
            print(f"  Row {i}: {row_str}")
        
        print(f"\nScaled Statistics:")
        print(f"  Min: {np.min(scaled_data):.1f} °KW")
        print(f"  Max: {np.max(scaled_data):.1f} °KW")
        print(f"  Mean: {np.mean(scaled_data):.1f} °KW")
        
        # Check if values are reasonable for ignition timing
        if -20 <= np.min(scaled_data) and np.max(scaled_data) <= 50:
            print("  ✓ Values look reasonable for ignition timing")
        else:
            print("  ✗ Values don't look like ignition timing")

def search_for_axis_patterns(rom_file: str):
    """Search for axis patterns in the ROM"""
    with open(rom_file, 'rb') as f:
        rom_data = f.read()
    
    print("=== SEARCHING FOR AXIS PATTERNS ===")
    
    # Look for RPM-like patterns (monotonically increasing uint16 values)
    print("\nSearching for RPM axes...")
    
    rpm_candidates = []
    
    # Search in common areas
    search_ranges = [(0x17000, 0x19000), (0x1F000, 0x21000)]
    
    for start_addr, end_addr in search_ranges:
        for addr in range(start_addr, min(end_addr, len(rom_data) - 32), 2):
            try:
                # Try to read 16 uint16 values
                values = []
                for i in range(16):
                    val = struct.unpack('<H', rom_data[addr + i*2:addr + i*2 + 2])[0]
                    # Apply common RPM scaling
                    rpm = val * 0.25
                    values.append(rpm)
                
                # Check if this looks like an RPM axis
                if (800 <= values[0] <= 1500 and  # Reasonable idle RPM
                    6000 <= values[-1] <= 8000 and  # Reasonable max RPM
                    all(values[i] <= values[i+1] for i in range(15))):  # Monotonic
                    
                    rpm_candidates.append((addr, values))
                    
            except:
                continue
    
    print(f"Found {len(rpm_candidates)} RPM axis candidates:")
    for addr, values in rpm_candidates[:5]:  # Show first 5
        print(f"  0x{addr:X}: {values[0]:.0f} to {values[-1]:.0f} RPM")
        print(f"    Values: {[f'{v:.0f}' for v in values[:8]]} ...")
    
    # Look for load axis patterns
    print(f"\nSearching for load axes...")
    
    load_candidates = []
    
    for start_addr, end_addr in search_ranges:
        for addr in range(start_addr, min(end_addr, len(rom_data) - 16), 1):
            try:
                # Try to read 16 uint8 values
                values = [rom_data[addr + i] for i in range(16)]
                
                # Check if this looks like a load axis (0-100% or similar)
                if (0 <= values[0] <= 20 and  # Low start
                    80 <= values[-1] <= 255 and  # High end
                    all(values[i] <= values[i+1] for i in range(15))):  # Monotonic
                    
                    load_candidates.append((addr, values))
                    
            except:
                continue
    
    print(f"Found {len(load_candidates)} load axis candidates:")
    for addr, values in load_candidates[:5]:  # Show first 5
        print(f"  0x{addr:X}: {values[0]} to {values[-1]} (raw)")
        print(f"    Values: {values[:8]} ...")

def main():
    rom_file = "IGNITec (VW Golf (LC PnB) - MED9.1.5 - 8E1910115G - 0010).bin"
    
    if not Path(rom_file).exists():
        print(f"Error: {rom_file} not found")
        return
    
    print("ECU Map Inspector")
    print("=" * 50)
    
    # Inspect the timing map
    inspect_timing_map_with_scaling(rom_file)
    
    print("\n" + "=" * 50)
    
    # Search for axis patterns
    search_for_axis_patterns(rom_file)
    
    print("\n" + "=" * 50)
    print("Inspection complete!")

if __name__ == '__main__':
    main()
