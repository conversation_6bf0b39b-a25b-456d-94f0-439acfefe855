#!/usr/bin/env python3
"""
Advanced WinOLS .kp file parser
Attempts to parse the actual binary structure of WinOLS map pack files
"""

import struct
from typing import Dict, List, Optional, Tuple
from pathlib import Path

class KPParser:
    """Advanced parser for WinOLS .kp files"""
    
    def __init__(self, kp_file: str):
        self.kp_file = Path(kp_file)
        self.maps = {}
        self.raw_data = None
        self.load_file()
    
    def load_file(self):
        """Load the .kp file into memory"""
        with open(self.kp_file, 'rb') as f:
            self.raw_data = f.read()
        print(f"Loaded {len(self.raw_data)} bytes from {self.kp_file}")
    
    def parse_header(self) -> Dict:
        """Parse the WinOLS file header"""
        if len(self.raw_data) < 32:
            raise ValueError("File too small to be a valid .kp file")
        
        # Check for WinOLS signature
        if self.raw_data[4:15] != b'WinOLS File':
            raise ValueError("Not a valid WinOLS file")
        
        header = {
            'signature': self.raw_data[4:15].decode('ascii'),
            'version_info': self.raw_data[16:32]
        }
        
        return header
    
    def find_string_at_offset(self, offset: int, max_length: int = 256) -> str:
        """Find a null-terminated string at the given offset"""
        if offset >= len(self.raw_data):
            return ""
        
        end_offset = offset
        while end_offset < len(self.raw_data) and end_offset < offset + max_length:
            if self.raw_data[end_offset] == 0:
                break
            end_offset += 1
        
        try:
            return self.raw_data[offset:end_offset].decode('ascii', errors='ignore')
        except:
            return ""
    
    def scan_for_map_entries(self) -> List[Dict]:
        """Scan the file for potential map entries"""
        maps = []
        offset = 0x100  # Start after header
        
        while offset < len(self.raw_data) - 64:
            # Look for patterns that might indicate map entries
            # WinOLS maps often have specific byte patterns
            
            # Check for potential address (4 bytes)
            if offset + 8 < len(self.raw_data):
                potential_addr = struct.unpack('<I', self.raw_data[offset:offset+4])[0]
                
                # Reasonable address range for MED9.1.5 (typically 0x10000 - 0x200000)
                if 0x10000 <= potential_addr <= 0x200000:
                    # Look for dimensions nearby
                    for dim_offset in range(offset + 4, min(offset + 32, len(self.raw_data) - 4)):
                        try:
                            x_size = struct.unpack('<H', self.raw_data[dim_offset:dim_offset+2])[0]
                            y_size = struct.unpack('<H', self.raw_data[dim_offset+2:dim_offset+4])[0]
                            
                            # Reasonable map dimensions
                            if 1 <= x_size <= 32 and 1 <= y_size <= 32:
                                # Look for a string name nearby
                                name = ""
                                for name_offset in range(max(0, offset - 64), min(offset + 64, len(self.raw_data))):
                                    potential_name = self.find_string_at_offset(name_offset, 32)
                                    if len(potential_name) > 2 and potential_name.isalnum():
                                        name = potential_name
                                        break
                                
                                map_entry = {
                                    'name': name or f"MAP_{potential_addr:X}",
                                    'address': potential_addr,
                                    'x_size': x_size,
                                    'y_size': y_size,
                                    'offset_in_kp': offset
                                }
                                
                                # Avoid duplicates
                                if not any(m['address'] == potential_addr for m in maps):
                                    maps.append(map_entry)
                        except:
                            continue
            
            offset += 1
        
        return maps
    
    def extract_maps_heuristic(self) -> Dict:
        """Extract maps using heuristic analysis"""
        print("Scanning for map entries...")
        potential_maps = self.scan_for_map_entries()
        
        print(f"Found {len(potential_maps)} potential maps")
        
        # Convert to our standard format
        maps = {}
        for i, map_entry in enumerate(potential_maps):
            map_name = map_entry['name'] or f"MAP_{i:02d}"
            
            maps[map_name] = {
                'name': f"{map_name} - Auto-detected",
                'address': map_entry['address'],
                'x_size': map_entry['x_size'],
                'y_size': map_entry['y_size'],
                'data_type': 'uint8',  # Default assumption
                'factor': 1.0,
                'offset': 0.0,
                'unit': 'raw',
                'x_axis_name': 'X Axis',
                'y_axis_name': 'Y Axis',
                'description': f'Auto-detected map at 0x{map_entry["address"]:X}'
            }
        
        return maps
    
    def dump_hex_analysis(self, output_file: str = "kp_analysis.txt"):
        """Dump hex analysis of the .kp file for manual inspection"""
        with open(output_file, 'w') as f:
            f.write(f"WinOLS .kp File Analysis: {self.kp_file}\n")
            f.write("=" * 60 + "\n\n")
            
            # Header analysis
            f.write("HEADER ANALYSIS:\n")
            f.write("-" * 20 + "\n")
            try:
                header = self.parse_header()
                f.write(f"Signature: {header['signature']}\n")
                f.write(f"Version info: {header['version_info'].hex()}\n")
            except Exception as e:
                f.write(f"Header parse error: {e}\n")
            
            f.write("\n")
            
            # Hex dump of first 512 bytes
            f.write("HEX DUMP (first 512 bytes):\n")
            f.write("-" * 30 + "\n")
            for i in range(0, min(512, len(self.raw_data)), 16):
                hex_part = ' '.join(f'{b:02x}' for b in self.raw_data[i:i+16])
                ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in self.raw_data[i:i+16])
                f.write(f"{i:04x}: {hex_part:<48} |{ascii_part}|\n")
            
            f.write("\n")
            
            # String analysis
            f.write("STRING ANALYSIS:\n")
            f.write("-" * 20 + "\n")
            strings_found = []
            for i in range(len(self.raw_data) - 3):
                string = self.find_string_at_offset(i, 64)
                if len(string) > 3 and string.isalnum():
                    strings_found.append((i, string))
            
            # Remove duplicates and sort
            unique_strings = list(set(s[1] for s in strings_found))
            for string in sorted(unique_strings):
                offsets = [s[0] for s in strings_found if s[1] == string]
                f.write(f"'{string}' at offsets: {[f'0x{o:x}' for o in offsets[:5]]}\n")
        
        print(f"Analysis saved to {output_file}")

def main():
    """Test the KP parser"""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python kp_parser.py <kp_file>")
        return
    
    kp_file = sys.argv[1]
    parser = KPParser(kp_file)
    
    # Create analysis file
    parser.dump_hex_analysis()
    
    # Try to extract maps
    maps = parser.extract_maps_heuristic()
    
    print(f"\nExtracted {len(maps)} maps:")
    for name, info in maps.items():
        print(f"  {name}: 0x{info['address']:X} ({info['x_size']}x{info['y_size']})")

if __name__ == '__main__':
    main()
